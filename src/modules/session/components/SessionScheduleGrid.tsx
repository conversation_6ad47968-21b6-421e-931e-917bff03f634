import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import type { Schedule, Turn } from "~/modules/schedule/service/model/schedule";
import {
	sessionOptions,
	sessionOptionsByClientAndTurn,
	sessionOptionsByWorkerAndTurn,
} from "~/modules/session/hooks/session-options";
import useBulkCreateSessions from "~/modules/session/hooks/use-bulk-create-sessions";
import useBulkDeleteSessions from "~/modules/session/hooks/use-bulk-delete-sessions";
import { sessionActions, sessionStore } from "~/modules/session/store/session";
import SessionModal from "./SessionModal";

interface SessionScheduleGridProps {
	schedule: Schedule;
	turn: Turn;
}

interface TimeSlot {
	start: number;
	end: number;
	label: string;
}

interface SessionModalData {
	dayIndex: number;
	timeIndex: number;
	isOpen: boolean;
}

export default function SessionScheduleGrid({
	schedule,
	turn,
}: SessionScheduleGridProps) {
	const svc = useService();
	const { selectedClient, selectedWorker } = useStore(sessionStore);
	const { mutate: bulkCreateSessions } = useBulkCreateSessions();
	const { mutate: bulkDeleteSessions } = useBulkDeleteSessions();
	const [sessionModal, setSessionModal] = useState<SessionModalData>({
		dayIndex: -1,
		timeIndex: -1,
		isOpen: false,
	});
	const [seeAll, setSeeAll] = useState(false);
	const [showNames, setShowNames] = useState(true);

	// Clear selected client and worker when "see all" is enabled
	useEffect(() => {
		if (seeAll) {
			sessionActions.setClient(null);
			sessionActions.setWorker(null);
		}
	}, [seeAll]);

	// Disable "see all" when a client or worker is selected
	useEffect(() => {
		if ((selectedClient || selectedWorker) && seeAll) {
			setSeeAll(false);
		}
	}, [selectedClient, selectedWorker, seeAll]);

	// Use filtered queries based on selected client or worker
	const { data: sessionsByClient } = useQuery({
		...sessionOptionsByClientAndTurn(svc, selectedClient?.id || "", turn.id),
		enabled: !!selectedClient && !!turn.id && !seeAll,
	});

	const { data: sessionsByWorker } = useQuery({
		...sessionOptionsByWorkerAndTurn(svc, selectedWorker?.id || "", turn.id),
		enabled: !!selectedWorker && !!turn.id && !seeAll,
	});

	// Query for all sessions when "see all" is enabled
	const { data: allSessionsData } = useQuery({
		...sessionOptions(svc),
		enabled: seeAll && !!turn.id,
	});

	// Filter all sessions by current turn when "see all" is enabled
	const allSessionsForTurn = seeAll
		? (allSessionsData || []).filter((session) => session.turnId === turn.id)
		: [];

	// Use the appropriate sessions data based on what's selected
	const sessions = seeAll
		? allSessionsForTurn
		: selectedClient
			? sessionsByClient
			: selectedWorker
				? sessionsByWorker
				: [];

	const minutesToTimeString = (minutes: number) => {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		const period = hours >= 12 ? "PM" : "AM";
		const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
		return `${displayHours}:${mins.toString().padStart(2, "0")} ${period}`;
	};

	const generateTimeSlots = (): TimeSlot[] => {
		const slots: TimeSlot[] = [];
		const sessionDuration = schedule.sessionDuration;
		const breakDuration = schedule.breakDuration;

		// Convert start and end times to minutes
		const startTimeInMinutes =
			Math.floor(turn.startTime / 100) * 60 + (turn.startTime % 100);
		const endTimeInMinutes =
			Math.floor(turn.endTime / 100) * 60 + (turn.endTime % 100);

		let currentTime = startTimeInMinutes;

		while (currentTime < endTimeInMinutes) {
			const sessionEnd = currentTime + sessionDuration;

			// Check if the complete session fits within the time window
			if (sessionEnd > endTimeInMinutes) {
				break; // Not enough time for a complete session
			}

			slots.push({
				start: currentTime,
				end: sessionEnd,
				label: "Sesión",
			});

			// Move to next session time (including break duration for spacing)
			currentTime = sessionEnd + breakDuration;
		}

		return slots;
	};

	const timeSlots = generateTimeSlots();
	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	const getSessionForSlot = (dayIndex: number, timeIndex: number) => {
		return sessions?.find(
			(session) => session.day === dayIndex && session.time === timeIndex,
		);
	};

	const getSessionsForSlot = (dayIndex: number, timeIndex: number) => {
		return (
			sessions?.filter(
				(session) => session.day === dayIndex && session.time === timeIndex,
			) || []
		);
	};

	const handleCellClick = (dayIndex: number, timeIndex: number) => {
		setSessionModal({
			dayIndex,
			timeIndex,
			isOpen: true,
		});
	};

	const handleModalClose = () => {
		setSessionModal({
			dayIndex: -1,
			timeIndex: -1,
			isOpen: false,
		});
	};

	// Helper functions for day-level operations
	const getSessionsForDay = (dayIndex: number) => {
		return sessions?.filter((session) => session.day === dayIndex) || [];
	};

	const getBusySessionsForDay = (dayIndex: number) => {
		return getSessionsForDay(dayIndex).filter(
			(session) => session.client.id === "0",
		);
	};

	const hasAvailableSlotsForDay = (dayIndex: number) => {
		const daySessions = getSessionsForDay(dayIndex);
		return daySessions.length < timeSlots.length;
	};

	const hasBusySessionsForDay = (dayIndex: number) => {
		return getBusySessionsForDay(dayIndex).length > 0;
	};

	const handleMakeDayBusy = (dayIndex: number) => {
		if (!selectedWorker) {
			toast.error(
				"Debe seleccionar un trabajador para marcar el día como ocupado",
			);
			return;
		}

		// Get all time slots that don't have sessions
		const daySessionTimes = getSessionsForDay(dayIndex).map((s) => s.time);
		const availableTimeSlots = timeSlots
			.map((_, index) => index)
			.filter((timeIndex) => !daySessionTimes.includes(timeIndex));

		if (availableTimeSlots.length === 0) {
			toast.info("No hay horarios disponibles para marcar como ocupado");
			return;
		}

		// Create busy sessions for all available slots
		const busySessions = availableTimeSlots.map((timeIndex) => ({
			clientId: "0",
			workerId: selectedWorker.id,
			turnId: turn.id,
			day: dayIndex,
			time: timeIndex,
		}));

		bulkCreateSessions(
			{ sessions: busySessions },
			{
				onSuccess: () => {
					toast.success(
						`Día marcado como ocupado (${busySessions.length} horarios)`,
					);
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			},
		);
	};

	const handleFreeDayBusy = (dayIndex: number) => {
		const busySessions = getBusySessionsForDay(dayIndex);

		if (busySessions.length === 0) {
			toast.info("No hay horarios ocupados para liberar");
			return;
		}

		const busySessionIds = busySessions.map((session) => session.id);

		bulkDeleteSessions(
			{ ids: busySessionIds },
			{
				onSuccess: () => {
					toast.success(`Día liberado (${busySessionIds.length} horarios)`);
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			},
		);
	};

	return (
		<>
			{/* Control switches */}
			<div className="mb-4 flex gap-4">
				<div className="form-control">
					<label className="label cursor-pointer">
						<span className="label-text mr-2">Ver todo</span>
						<input
							type="checkbox"
							className="toggle toggle-primary"
							checked={seeAll}
							disabled={!turn.id}
							onChange={(e) => setSeeAll(e.target.checked)}
						/>
					</label>
				</div>
				<div className="form-control">
					<label className="label cursor-pointer">
						<span className="label-text mr-2">Mostrar nombres</span>
						<input
							type="checkbox"
							className="toggle toggle-primary"
							checked={showNames}
							onChange={(e) => setShowNames(e.target.checked)}
						/>
					</label>
				</div>
			</div>

			<div className="overflow-x-auto">
				<div
					className="grid gap-1 text-xs"
					style={{
						gridTemplateColumns: `120px repeat(${days.length}, 1fr)`,
						gridTemplateRows: `auto repeat(${timeSlots.length}, minmax(64px, auto))`,
					}}
				>
					{/* Header row */}
					<div className="rounded bg-base-200 p-2 font-medium">Hora</div>
					{days.map((day, dayIndex) => (
						<div
							key={day}
							className="rounded bg-base-200 p-2 text-center font-medium"
						>
							<div className="mb-1">{day}</div>
							{/* Day-level controls - only show when worker is selected and see all is not active */}
							{selectedWorker && !seeAll && (
								<div className="flex justify-center gap-1">
									{hasAvailableSlotsForDay(dayIndex) && (
										<button
											type="button"
											className="btn btn-xs btn-warning"
											onClick={() => handleMakeDayBusy(dayIndex)}
											title="Marcar día como ocupado"
										>
											Ocupar
										</button>
									)}
									{hasBusySessionsForDay(dayIndex) && (
										<button
											type="button"
											className="btn btn-xs btn-success"
											onClick={() => handleFreeDayBusy(dayIndex)}
											title="Liberar día"
										>
											Liberar
										</button>
									)}
								</div>
							)}
						</div>
					))}

					{/* Time slots and content */}
					{timeSlots.map((slot, timeIndex) => (
						<React.Fragment key={`${slot.start}-${timeIndex}`}>
							<div className="flex items-center rounded bg-base-100 p-2 font-mono text-xs">
								{minutesToTimeString(slot.start)} -{" "}
								{minutesToTimeString(slot.end)}
							</div>
							{days.map((day, dayIndex) => {
								const existingSession = getSessionForSlot(dayIndex, timeIndex);
								const allSessionsInSlot = getSessionsForSlot(
									dayIndex,
									timeIndex,
								);
								const hasMultipleSessions =
									seeAll && allSessionsInSlot.length > 1;
								const hasSessions = allSessionsInSlot.length > 0;

								return (
									<div
										key={day}
										className="flex items-center justify-center p-1"
									>
										<button
											type="button"
											className={`flex w-full items-center justify-center rounded px-2 py-1 font-medium text-xs transition-colors ${
												hasSessions
													? hasMultipleSessions
														? "bg-info text-info-content hover:bg-info/80"
														: existingSession?.client.id === "0"
															? "bg-warning text-warning-content hover:bg-warning/80"
															: "bg-success text-success-content hover:bg-success/80"
													: "bg-primary text-primary-content hover:bg-primary/80"
											} ${hasMultipleSessions ? "h-20" : "h-16"}`}
											onClick={() => handleCellClick(dayIndex, timeIndex)}
										>
											{hasSessions ? (
												<div className="w-full text-center">
													{hasMultipleSessions ? (
														<div className="space-y-1">
															<div className="font-semibold text-xs">
																{allSessionsInSlot.length} sesiones
															</div>
															{showNames && (
																<div className="space-y-0.5 text-xs opacity-80">
																	{allSessionsInSlot
																		.slice(0, 2)
																		.map((session) => (
																			<div
																				key={session.id}
																				className="truncate"
																			>
																				{session.client.id === "0"
																					? "Ocupado"
																					: `${session.client.person.name} - ${session.worker.person.name}`}
																			</div>
																		))}
																	{allSessionsInSlot.length > 2 && (
																		<div className="text-xs">
																			+{allSessionsInSlot.length - 2} más
																		</div>
																	)}
																</div>
															)}
														</div>
													) : existingSession?.client.id === "0" ? (
														<div className="font-semibold">Ocupado</div>
													) : (
														<>
															{showNames ? (
																<>
																	<div className="font-semibold">
																		{existingSession?.client.person.name}
																	</div>
																	<div className="text-xs opacity-80">
																		{existingSession?.worker.person.name}
																	</div>
																</>
															) : (
																<div className="font-semibold">Ocupado</div>
															)}
														</>
													)}
												</div>
											) : (
												"Disponible"
											)}
										</button>
									</div>
								);
							})}
						</React.Fragment>
					))}
				</div>
			</div>

			<SessionModal
				isOpen={sessionModal.isOpen}
				onClose={handleModalClose}
				dayIndex={sessionModal.dayIndex}
				timeIndex={sessionModal.timeIndex}
				existingSession={getSessionForSlot(
					sessionModal.dayIndex,
					sessionModal.timeIndex,
				)}
			/>
		</>
	);
}
