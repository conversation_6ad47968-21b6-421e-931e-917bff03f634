import { Store } from "@tanstack/react-store";
import type { Client } from "~/modules/client/service/model/client";
import type { Schedule, Turn } from "~/modules/schedule/service/model/schedule";
import type { Worker } from "~/modules/worker/service/model/worker";

interface SessionState {
	selectedSchedule: Schedule | null;
	selectedTurn: Turn | null;
	selectedClient: Client | null;
	selectedWorker: Worker | null;
	seeAll: boolean;
}

const initialState: SessionState = {
	selectedSchedule: null,
	selectedTurn: null,
	selectedClient: null,
	selectedWorker: null,
	seeAll: false,
};

export const sessionStore = new Store<SessionState>(initialState);

export const sessionActions = {
	setSchedule: (schedule: Schedule | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedSchedule: schedule,
			selectedTurn: null,
			selectedClient: null,
			selectedWorker: null,
		}));
	},
	setTurn: (turn: Turn | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedTurn: turn,
			selectedClient: null,
			selectedWorker: null,
		}));
	},
	setClient: (client: Client | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedClient: client,
			// If a client is selected, clear the worker and disable see all
			selectedWorker: client ? null : prev.selectedWorker,
			seeAll: client ? false : prev.seeAll,
		}));
	},
	setWorker: (worker: Worker | null) => {
		sessionStore.setState((prev) => ({
			...prev,
			selectedWorker: worker,
			// If a worker is selected, clear the client and disable see all
			selectedClient: worker ? null : prev.selectedClient,
			seeAll: worker ? false : prev.seeAll,
		}));
	},
	setSeeAll: (seeAll: boolean) => {
		sessionStore.setState((prev) => ({
			...prev,
			seeAll,
			// If enabling see all, clear client and worker selections
			selectedClient: seeAll ? null : prev.selectedClient,
			selectedWorker: seeAll ? null : prev.selectedWorker,
		}));
	},
	clearAll: () => {
		sessionStore.setState(initialState);
	},
};
